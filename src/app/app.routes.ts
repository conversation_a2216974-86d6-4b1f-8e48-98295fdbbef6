import { Routes } from '@angular/router';
import { LayoutComponent } from './layout/layout.component';
import { AuthGuard, AdminGuard, ManagerGuard } from './core/auth/auth.guard';

export const routes: Routes = [
  // Authentication routes (no layout)
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
      },
      {
        path: 'register',
        loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
      },
      { path: '', redirectTo: 'login', pathMatch: 'full' }
    ]
  },
  // Protected routes (with layout)
  {
    path: '',
    component: LayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/enhanced-dashboard.component').then(m => m.EnhancedDashboardComponent)
      },
      {
        path: 'staff',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/staff/staff-list/staff-list.component').then(m => m.StaffListComponent)
          },
          {
            path: 'profile/:id',
            loadComponent: () => import('./features/staff/staff-profile/staff-profile.component').then(m => m.StaffProfileComponent)
          },
          {
            path: 'create',
            loadComponent: () => import('./features/staff/staff-form/staff-form.component').then(m => m.StaffFormComponent),
            canActivate: [ManagerGuard]
          },
          {
            path: 'edit/:id',
            loadComponent: () => import('./features/staff/staff-form/staff-form.component').then(m => m.StaffFormComponent),
            canActivate: [ManagerGuard]
          }
        ]
      },
      {
        path: 'calendar',
        loadComponent: () => import('./features/calendar/calendar.component').then(m => m.CalendarComponent)
      },
      {
        path: 'tasks',
        loadComponent: () => import('./features/tasks/tasks.component').then(m => m.TasksComponent)
      },
      {
        path: 'time',
        loadComponent: () => import('./features/time-management/time-management.component').then(m => m.TimeManagementComponent)
      },
      {
        path: 'settings',
        loadComponent: () => import('./features/settings/settings.component').then(m => m.SettingsComponent)
      },
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
    ]
  },
  { path: '**', redirectTo: 'auth/login' }
];
