import { Injectable, inject } from '@angular/core';
import { Observable, from, map, switchMap, combineLatest } from 'rxjs';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { StaffFirestoreService } from '../../features/staff/services/staff-firestore.service';
import { CalendarService } from '../../features/calendar/services/calendar.service';
import { StaffMember, StaffTask, StaffGoalExtended } from '../../features/staff/models/staff.model';
import { CalendarEvent } from '../../features/calendar/models/calendar.model';
import { environment } from '../../../environments/environment';

export interface AIRecommendation {
  type: 'scheduling' | 'task-assignment' | 'goal-optimization' | 'workload-balance';
  title: string;
  description: string;
  confidence: number; // 0-1
  actionItems: string[];
  affectedStaff?: string[];
  priority: 'low' | 'medium' | 'high';
}

export interface SchedulingSuggestion {
  staffId: string;
  staffName: string;
  suggestedTime: Date;
  reason: string;
  confidence: number;
}

@Injectable({
  providedIn: 'root'
})
export class AIAssistantService {
  private staffService = inject(StaffFirestoreService);
  private calendarService = inject(CalendarService);
  private genAI: GoogleGenerativeAI;

  constructor() {
    // Initialize Google AI with environment API key
    this.genAI = new GoogleGenerativeAI(environment.googleAI.apiKey);
  }

  // Smart Staff Scheduling Recommendations
  getSchedulingRecommendations(businessId: string, date: Date): Observable<SchedulingSuggestion[]> {
    return combineLatest([
      this.staffService.getActiveStaff(businessId),
      this.calendarService.getEvents(businessId, date, date)
    ]).pipe(
      map(([staff, events]) => this.analyzeSchedulingNeeds(staff, events, date))
    );
  }

  // Intelligent Task Assignment
  suggestTaskAssignment(task: Partial<StaffTask>, businessId: string): Observable<SchedulingSuggestion[]> {
    return this.staffService.getActiveStaff(businessId).pipe(
      switchMap(staff => this.analyzeTaskFit(task, staff))
    );
  }

  // Goal Progress Analysis and Recommendations
  analyzeGoalProgress(staffId: string): Observable<AIRecommendation[]> {
    return combineLatest([
      this.staffService.subscribeToStaffGoals(staffId),
      this.staffService.subscribeToStaffTasks(staffId)
    ]).pipe(
      map(([goals, tasks]) => this.generateGoalRecommendations(goals, tasks))
    );
  }

  // Workload Balance Analysis
  analyzeWorkloadBalance(businessId: string): Observable<AIRecommendation[]> {
    return combineLatest([
      this.staffService.getActiveStaff(businessId),
      this.calendarService.getEvents(businessId, new Date(), this.getDateInDays(7))
    ]).pipe(
      map(([staff, events]) => this.analyzeWorkloadDistribution(staff, events))
    );
  }

  // AI-Powered Chat Assistant for Staff Management
  async askStaffManagementQuestion(question: string, context: any): Promise<string> {
    try {
      const model = this.genAI.getGenerativeModel({ model: "gemini-pro" });

      const prompt = `
        You are an AI assistant for StaffManager, a staff management application.

        Context: ${JSON.stringify(context, null, 2)}

        Question: ${question}

        Please provide helpful advice for staff management, scheduling, task assignment, or goal setting.
        Keep your response practical and actionable.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('AI Assistant Error:', error);
      return 'I apologize, but I cannot process your request at the moment. Please try again later.';
    }
  }

  // Generate automated shift schedules
  generateOptimalSchedule(
    staff: StaffMember[],
    requirements: any,
    dateRange: { start: Date; end: Date }
  ): Observable<CalendarEvent[]> {
    return from(this.calculateOptimalSchedule(staff, requirements, dateRange));
  }

  // Private helper methods
  private analyzeSchedulingNeeds(staff: StaffMember[], events: CalendarEvent[], date: Date): SchedulingSuggestion[] {
    const suggestions: SchedulingSuggestion[] = [];

    // Analyze staff availability and workload
    staff.forEach(member => {
      const memberEvents = events.filter(e => e.assignedStaff.includes(member.id || ''));
      const workload = this.calculateWorkload(memberEvents);

      if (workload < 0.7) { // Less than 70% capacity
        suggestions.push({
          staffId: member.id || '',
          staffName: `${member.firstName} ${member.lastName}`,
          suggestedTime: new Date(date.getTime() + 9 * 60 * 60 * 1000), // 9 AM
          reason: 'Available capacity for additional shifts',
          confidence: 0.8
        });
      }
    });

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  private async analyzeTaskFit(task: Partial<StaffTask>, staff: StaffMember[]): Promise<SchedulingSuggestion[]> {
    const suggestions: SchedulingSuggestion[] = [];

    for (const member of staff) {
      let score = 0;
      let reasons: string[] = [];

      // Skill matching
      if (task.category && member.skills) {
        const relevantSkills = member.skills.filter(skill =>
          skill.category.toLowerCase().includes(task.category?.toLowerCase() || '')
        );
        if (relevantSkills.length > 0) {
          score += 0.4;
          reasons.push('Has relevant skills');
        }
      }

      // Workload consideration
      const currentTasks = await this.staffService.subscribeToStaffTasks(member.id || '').toPromise();
      const activeTasks = currentTasks?.filter(t => t.status === 'in-progress' || t.status === 'pending') || [];

      if (activeTasks.length < 3) {
        score += 0.3;
        reasons.push('Low current workload');
      }

      // Availability
      if (member.status === 'active') {
        score += 0.3;
        reasons.push('Currently active');
      }

      if (score > 0.5) {
        suggestions.push({
          staffId: member.id || '',
          staffName: `${member.firstName} ${member.lastName}`,
          suggestedTime: new Date(),
          reason: reasons.join(', '),
          confidence: score
        });
      }
    }

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  private generateGoalRecommendations(goals: StaffGoalExtended[], tasks: StaffTask[]): AIRecommendation[] {
    const recommendations: AIRecommendation[] = [];

    goals.forEach(goal => {
      if (goal.progress < 50 && this.isNearDeadline(goal.targetDate)) {
        recommendations.push({
          type: 'goal-optimization',
          title: `Goal "${goal.title}" needs attention`,
          description: `This goal is ${goal.progress}% complete with deadline approaching`,
          confidence: 0.9,
          actionItems: [
            'Break down remaining work into smaller tasks',
            'Allocate more time daily to this goal',
            'Consider extending deadline if necessary'
          ],
          priority: 'high'
        });
      }
    });

    return recommendations;
  }

  private analyzeWorkloadDistribution(staff: StaffMember[], events: CalendarEvent[]): AIRecommendation[] {
    const recommendations: AIRecommendation[] = [];
    const workloads = new Map<string, number>();

    // Calculate workload for each staff member
    staff.forEach(member => {
      const memberEvents = events.filter(e => e.assignedStaff.includes(member.id || ''));
      workloads.set(member.id || '', this.calculateWorkload(memberEvents));
    });

    // Find imbalances
    const avgWorkload = Array.from(workloads.values()).reduce((a, b) => a + b, 0) / workloads.size;
    const overloaded = Array.from(workloads.entries()).filter(([_, load]) => load > avgWorkload * 1.3);
    const underloaded = Array.from(workloads.entries()).filter(([_, load]) => load < avgWorkload * 0.7);

    if (overloaded.length > 0 && underloaded.length > 0) {
      recommendations.push({
        type: 'workload-balance',
        title: 'Workload imbalance detected',
        description: 'Some staff members are overloaded while others have capacity',
        confidence: 0.85,
        actionItems: [
          'Redistribute tasks from overloaded to underloaded staff',
          'Review scheduling patterns',
          'Consider hiring additional staff if consistently overloaded'
        ],
        affectedStaff: [...overloaded.map(([id]) => id), ...underloaded.map(([id]) => id)],
        priority: 'medium'
      });
    }

    return recommendations;
  }

  private async calculateOptimalSchedule(
    staff: StaffMember[],
    requirements: any,
    dateRange: { start: Date; end: Date }
  ): Promise<CalendarEvent[]> {
    // This would implement a more sophisticated scheduling algorithm
    // For now, return a simple example
    const events: CalendarEvent[] = [];

    // Simple round-robin scheduling
    let staffIndex = 0;
    const currentDate = new Date(dateRange.start);

    while (currentDate <= dateRange.end) {
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) { // Weekdays only
        const selectedStaff = staff[staffIndex % staff.length];

        events.push({
          id: `auto-${Date.now()}-${staffIndex}`,
          title: `Shift - ${selectedStaff.firstName} ${selectedStaff.lastName}`,
          start: new Date(currentDate.getTime() + 9 * 60 * 60 * 1000), // 9 AM
          end: new Date(currentDate.getTime() + 17 * 60 * 60 * 1000), // 5 PM
          type: 'shift',
          status: 'scheduled',
          assignedStaff: [selectedStaff.id || ''],
          createdBy: 'ai-assistant',
          businessId: selectedStaff.primaryBusinessId,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        staffIndex++;
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return events;
  }

  private calculateWorkload(events: CalendarEvent[]): number {
    // Calculate workload as a percentage of full capacity
    const totalHours = events.reduce((sum, event) => {
      const duration = (event.end.getTime() - event.start.getTime()) / (1000 * 60 * 60);
      return sum + duration;
    }, 0);

    const maxWeeklyHours = 40;
    return Math.min(totalHours / maxWeeklyHours, 1);
  }

  private isNearDeadline(targetDate: Date): boolean {
    const now = new Date();
    const daysUntilDeadline = (targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
    return daysUntilDeadline <= 7; // Within 7 days
  }

  private getDateInDays(days: number): Date {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date;
  }
}
