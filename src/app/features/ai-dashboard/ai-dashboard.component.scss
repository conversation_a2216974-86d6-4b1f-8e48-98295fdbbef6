.ai-dashboard-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .dashboard-header {
    text-align: center;
    margin-bottom: 32px;

    h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 2.5rem;
      font-weight: 500;
      background: linear-gradient(45deg, #1976d2, #42a5f5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      mat-icon {
        font-size: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
        color: #1976d2;
      }
    }

    p {
      font-size: 1.125rem;
      color: #666;
      margin: 0;
    }
  }

  .ai-tabs {
    .tab-content {
      padding: 24px 0;
    }
  }

  // Recommendations Tab
  .recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    .recommendation-card {
      border-left: 4px solid #1976d2;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }

      &.priority-high {
        border-left-color: #f44336;
        
        mat-card-header {
          background: linear-gradient(90deg, #ffebee, transparent);
        }
      }

      &.priority-medium {
        border-left-color: #ff9800;
        
        mat-card-header {
          background: linear-gradient(90deg, #fff3e0, transparent);
        }
      }

      &.priority-low {
        border-left-color: #4caf50;
        
        mat-card-header {
          background: linear-gradient(90deg, #e8f5e8, transparent);
        }
      }

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1.125rem;
      }

      .action-items {
        margin: 16px 0;

        h4 {
          margin: 0 0 8px 0;
          font-size: 0.875rem;
          font-weight: 600;
          color: #333;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
            font-size: 0.875rem;
            color: #555;
          }
        }
      }

      .affected-staff {
        margin-top: 16px;

        h4 {
          margin: 0 0 8px 0;
          font-size: 0.875rem;
          font-weight: 600;
          color: #333;
        }

        mat-chip-set {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }
      }
    }
  }

  // Scheduling Tab
  .scheduling-section {
    .scheduling-card {
      .date-selector {
        display: flex;
        gap: 16px;
        align-items: flex-end;
        margin-bottom: 24px;

        mat-form-field {
          flex: 1;
          max-width: 200px;
        }
      }

      .suggestions-list {
        max-height: 400px;
        overflow-y: auto;
      }
    }
  }

  // Chat Tab
  .chat-card {
    height: 600px;
    display: flex;
    flex-direction: column;

    mat-card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 16px 0;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 16px;
      background: #fafafa;

      .chat-message {
        margin-bottom: 16px;
        padding: 12px 16px;

        &.user {
          .message-content {
            background: #e3f2fd;
            border-radius: 18px 18px 4px 18px;
            padding: 12px 16px;
            margin-left: 20%;
          }
        }

        &.assistant {
          .message-content {
            background: white;
            border-radius: 18px 18px 18px 4px;
            padding: 12px 16px;
            margin-right: 20%;
            border: 1px solid #e0e0e0;
          }
        }

        .message-content {
          strong {
            color: #1976d2;
            font-size: 0.875rem;
          }

          p {
            margin: 4px 0 0 0;
            line-height: 1.5;
          }
        }

        .message-time {
          font-size: 0.75rem;
          color: #999;
          margin-top: 4px;
          text-align: right;
        }
      }
    }

    .chat-input {
      display: flex;
      gap: 12px;
      align-items: flex-end;

      .full-width {
        flex: 1;
      }

      button {
        height: 56px;
        min-width: 100px;

        .spinning {
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  // Insights Tab
  .insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;

    .insight-card {
      min-height: 200px;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  // Confidence level styling
  .confidence-high {
    background-color: #4caf50;
    color: white;
  }

  .confidence-medium {
    background-color: #ff9800;
    color: white;
  }

  .confidence-low {
    background-color: #f44336;
    color: white;
  }

  // Chip styling
  mat-chip {
    font-size: 0.75rem;
    min-height: 24px;
    padding: 0 8px;
    border-radius: 12px;
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .ai-dashboard-container {
    padding: 16px;

    .dashboard-header h1 {
      font-size: 2rem;
      
      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
      }
    }

    .recommendations-grid {
      grid-template-columns: 1fr;
    }

    .insights-grid {
      grid-template-columns: 1fr;
    }

    .chat-card {
      height: 500px;

      .chat-messages {
        .chat-message {
          &.user .message-content {
            margin-left: 10%;
          }

          &.assistant .message-content {
            margin-right: 10%;
          }
        }
      }

      .chat-input {
        flex-direction: column;
        align-items: stretch;

        button {
          height: 48px;
        }
      }
    }

    .scheduling-section .scheduling-card .date-selector {
      flex-direction: column;
      align-items: stretch;

      mat-form-field {
        max-width: none;
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .ai-dashboard-container {
    .chat-card .chat-messages {
      background: #303030;
      border-color: #424242;

      .chat-message {
        &.user .message-content {
          background: #1976d2;
          color: white;
        }

        &.assistant .message-content {
          background: #424242;
          color: white;
          border-color: #616161;
        }
      }
    }
  }
}
