/**
 * Calendar and Scheduling Models
 * For FullCalendar integration and staff scheduling
 */

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  type: 'shift' | 'meeting' | 'training' | 'time-off' | 'break' | 'appointment' | 'other';
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  
  // Staff assignment
  assignedStaff: string[]; // Staff IDs
  createdBy: string;
  businessId: string;
  location?: string;
  
  // Recurring events
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
  recurringEndDate?: Date;
  parentEventId?: string; // For recurring event instances
  
  // Additional properties
  color?: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  
  // Metadata
  notes?: string;
  attachments?: string[];
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  
  // System fields
  createdAt: Date;
  updatedAt: Date;
  
  // Notifications
  reminders?: EventReminder[];
  
  // Approval workflow
  requiresApproval?: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  approvalStatus?: 'pending' | 'approved' | 'rejected';
}

export interface RecurringPattern {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc. (for weekly)
  dayOfMonth?: number; // For monthly
  weekOfMonth?: number; // For monthly (1st, 2nd, 3rd, 4th, last)
  monthOfYear?: number; // For yearly
  endDate?: Date;
  occurrences?: number; // Number of occurrences
}

export interface EventReminder {
  id: string;
  type: 'email' | 'push' | 'sms';
  minutesBefore: number;
  sent?: boolean;
  sentAt?: Date;
}

// Shift-specific models
export interface Shift extends CalendarEvent {
  type: 'shift';
  position: string;
  department: string;
  hourlyRate?: number;
  breakDuration?: number; // minutes
  
  // Shift requirements
  minimumStaff: number;
  maximumStaff: number;
  requiredSkills?: string[];
  
  // Shift status
  clockInTime?: Date;
  clockOutTime?: Date;
  actualHours?: number;
  scheduledHours: number;
  
  // Shift swapping
  swapRequests?: ShiftSwapRequest[];
  canBeSwapped?: boolean;
}

export interface ShiftSwapRequest {
  id: string;
  requestedBy: string;
  requestedWith?: string; // Staff ID to swap with
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  requestedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
}

// Calendar view configurations
export interface CalendarViewConfig {
  id: string;
  name: string;
  type: 'month' | 'week' | 'day' | 'list' | 'timeline';
  defaultView: boolean;
  filters: CalendarFilter;
  permissions: CalendarPermission[];
  businessId: string;
  createdBy: string;
  isPublic: boolean;
}

export interface CalendarFilter {
  eventTypes?: string[];
  staffIds?: string[];
  departments?: string[];
  locations?: string[];
  statuses?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface CalendarPermission {
  userId: string;
  role: 'viewer' | 'editor' | 'admin';
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canApprove: boolean;
}

// Calendar integration
export interface ExternalCalendarSync {
  id: string;
  provider: 'google' | 'outlook' | 'apple' | 'other';
  accountEmail: string;
  calendarId: string;
  syncDirection: 'import' | 'export' | 'bidirectional';
  lastSyncAt?: Date;
  syncStatus: 'active' | 'paused' | 'error';
  errorMessage?: string;
  staffId: string;
  businessId: string;
}

// Availability and scheduling
export interface StaffAvailabilitySlot {
  id: string;
  staffId: string;
  date: Date;
  startTime: string; // HH:mm
  endTime: string; // HH:mm
  available: boolean;
  reason?: string; // If not available
  type: 'regular' | 'overtime' | 'on-call' | 'unavailable';
  businessId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SchedulingRule {
  id: string;
  name: string;
  description: string;
  type: 'minimum-hours' | 'maximum-hours' | 'rest-period' | 'skill-requirement' | 'availability-check';
  businessId: string;
  isActive: boolean;
  
  // Rule parameters
  parameters: {
    [key: string]: any;
  };
  
  // Rule conditions
  conditions?: {
    departments?: string[];
    positions?: string[];
    staffIds?: string[];
    eventTypes?: string[];
  };
  
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Calendar statistics and analytics
export interface CalendarStats {
  businessId: string;
  period: {
    start: Date;
    end: Date;
  };
  totalEvents: number;
  eventsByType: { [type: string]: number };
  totalScheduledHours: number;
  totalActualHours: number;
  attendanceRate: number;
  noShowRate: number;
  averageShiftLength: number;
  mostActiveStaff: {
    staffId: string;
    staffName: string;
    hoursScheduled: number;
    hoursWorked: number;
  }[];
  busyDays: {
    date: Date;
    eventCount: number;
    totalHours: number;
  }[];
}
