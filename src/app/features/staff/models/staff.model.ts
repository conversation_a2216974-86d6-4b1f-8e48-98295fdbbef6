/**
 * Staff Management Models
 * Based on the comprehensive staff system from previous iterations
 */

export interface StaffMember {
  id: string;
  employeeId: string;

  // Basic Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: Date;
  hireDate: Date;

  // Profile
  avatar?: string;
  bio?: string;
  position: string;
  department: string;
  manager?: string;

  // Employment Details
  employmentType: 'full-time' | 'part-time' | 'contract' | 'intern';
  status: 'active' | 'inactive' | 'on-leave' | 'terminated';
  salary?: number;
  hourlyRate?: number;

  // Contact Information
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  };

  // Skills and Qualifications
  skills: StaffSkill[];
  certifications: StaffCertification[];
  education: StaffEducation[];
  workExperience: StaffWorkExperience[];

  // Availability and Scheduling
  availability: StaffAvailability;
  timeZone: string;

  // System Fields
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastLoginAt?: Date;

  // Business/Location Assignment
  businessIds: string[];
  primaryBusinessId: string;

  // Permissions and Access
  roles: string[];
  permissions: string[];
  accessLevel: 'basic' | 'advanced' | 'admin' | 'super-admin';

  // Performance and Notes
  performanceRating?: number;
  notes?: StaffNote[];
  tags?: string[];
}

export interface StaffSkill {
  id: string;
  name: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  verified: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;
  yearsOfExperience?: number;
}

export interface StaffCertification {
  id: string;
  name: string;
  issuingOrganization: string;
  issueDate: Date;
  expirationDate?: Date;
  credentialId?: string;
  credentialUrl?: string;
  verified: boolean;
}

export interface StaffEducation {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy: string;
  startDate: Date;
  endDate?: Date;
  gpa?: number;
  graduated: boolean;
  description?: string;
}

export interface StaffWorkExperience {
  id: string;
  company: string;
  position: string;
  startDate: Date;
  endDate?: Date;
  current: boolean;
  description?: string;
  achievements?: string[];
}

export interface StaffAvailability {
  monday: DayAvailability;
  tuesday: DayAvailability;
  wednesday: DayAvailability;
  thursday: DayAvailability;
  friday: DayAvailability;
  saturday: DayAvailability;
  sunday: DayAvailability;

  // Special availability rules
  blackoutDates?: Date[];
  preferredShifts?: string[];
  maxHoursPerWeek?: number;
  minHoursPerWeek?: number;
}

export interface DayAvailability {
  available: boolean;
  startTime?: string; // HH:mm format
  endTime?: string;   // HH:mm format
  breaks?: TimeSlot[];
  notes?: string;
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  type?: 'break' | 'lunch' | 'meeting';
}

export interface StaffNote {
  id: string;
  content: string;
  type: 'general' | 'performance' | 'disciplinary' | 'achievement' | 'training';
  priority: 'low' | 'medium' | 'high';
  createdBy: string;
  createdAt: Date;
  isPrivate: boolean;
  tags?: string[];
}

// Filter and Search Interfaces
export interface StaffFilter {
  search?: string;
  departments?: string[];
  positions?: string[];
  employmentTypes?: string[];
  statuses?: string[];
  skills?: string[];
  businessIds?: string[];
  availableOn?: Date;
  hiredAfter?: Date;
  hiredBefore?: Date;
}

export interface StaffSortOptions {
  field: keyof StaffMember;
  direction: 'asc' | 'desc';
}

// Pagination
export interface StaffPaginationOptions {
  page: number;
  pageSize: number;
  totalCount?: number;
}

export interface StaffListResponse {
  staff: StaffMember[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
  };
  filters?: StaffFilter;
  sort?: StaffSortOptions;
}

// Staff Hub (Employee Portal) Models
export interface StaffHubProfile {
  staffId: string;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    dashboard: {
      widgets: string[];
      layout: any;
    };
  };
  quickActions: string[];
  recentActivity: StaffActivity[];
}

export interface StaffActivity {
  id: string;
  type: 'login' | 'schedule-change' | 'task-completed' | 'message-sent' | 'profile-updated';
  description: string;
  timestamp: Date;
  metadata?: any;
}

// Performance and Analytics
export interface StaffPerformanceMetrics {
  staffId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    attendanceRate: number;
    punctualityScore: number;
    tasksCompleted: number;
    customerRating?: number;
    hoursWorked: number;
    overtimeHours: number;
  };
  goals: StaffGoal[];
  feedback: StaffFeedback[];
}

export interface StaffGoal {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  status: 'not-started' | 'in-progress' | 'completed' | 'overdue';
  progress: number; // 0-100
  createdBy: string;
  createdAt: Date;
}

export interface StaffFeedback {
  id: string;
  fromUserId: string;
  fromUserName: string;
  type: 'positive' | 'constructive' | 'neutral';
  content: string;
  rating?: number; // 1-5
  createdAt: Date;
  isAnonymous: boolean;
}

// Enhanced Goal Management
export interface StaffGoalExtended extends StaffGoal {
  type: 'individual' | 'team' | 'company';
  category: 'sales' | 'performance' | 'training' | 'attendance' | 'custom';
  targetValue?: number;
  currentValue?: number;
  unit?: string; // 'dollars', 'units', 'hours', 'percentage'
  assignedBy: string;
  assignedTo: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  tags?: string[];
  attachments?: string[];
  comments?: GoalComment[];
  milestones?: GoalMilestone[];
  isRecurring?: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

export interface GoalComment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: Date;
}

export interface GoalMilestone {
  id: string;
  title: string;
  description?: string;
  targetDate: Date;
  completed: boolean;
  completedAt?: Date;
  completedBy?: string;
}

// Task Management
export interface StaffTask {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'administrative' | 'customer-service' | 'training' | 'maintenance' | 'project' | 'custom';
  assignedTo: string[];
  assignedBy: string;
  createdBy: string;
  dueDate?: Date;
  startDate?: Date;
  completedDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  attachments?: string[];
  comments?: TaskComment[];
  subtasks?: SubTask[];
  dependencies?: string[]; // Task IDs that must be completed first
  businessId: string;
  location?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskComment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: Date;
  attachments?: string[];
}

export interface SubTask {
  id: string;
  title: string;
  completed: boolean;
  completedAt?: Date;
  completedBy?: string;
}

// Time Management
export interface TimeEntry {
  id: string;
  staffId: string;
  type: 'clock-in' | 'clock-out' | 'break-start' | 'break-end' | 'lunch-start' | 'lunch-end';
  timestamp: Date;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  notes?: string;
  businessId: string;
  shiftId?: string;
  approved?: boolean;
  approvedBy?: string;
  approvedAt?: Date;
}

export interface TimeOffRequest {
  id: string;
  staffId: string;
  type: 'vacation' | 'sick' | 'personal' | 'bereavement' | 'jury-duty' | 'other';
  startDate: Date;
  endDate: Date;
  totalDays: number;
  totalHours: number;
  reason?: string;
  status: 'pending' | 'approved' | 'denied' | 'cancelled';
  requestedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  businessId: string;
  isEmergency?: boolean;
  attachments?: string[];
}

export interface WorkSchedule {
  id: string;
  staffId: string;
  date: Date;
  startTime: string; // HH:mm format
  endTime: string;
  breakDuration?: number; // minutes
  position?: string;
  location?: string;
  businessId: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'no-show' | 'cancelled';
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}
