import { Component, OnInit, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, combineLatest, of } from 'rxjs';
import { map, switchMap, shareReplay } from 'rxjs/operators';

// Angular Material
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';

// Services
import { StaffFirestoreService } from '../services/staff-firestore.service';
import { CalendarService } from '../../calendar/services/calendar.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { 
  StaffMember, 
  StaffGoalExtended, 
  StaffTask, 
  TimeEntry, 
  TimeOffRequest, 
  WorkSchedule 
} from '../models/staff.model';
import { CalendarEvent } from '../../calendar/models/calendar.model';

@Component({
  selector: 'app-staff-profile',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressBarModule,
    MatBadgeModule,
    MatMenuModule,
    MatDividerModule,
    MatListModule
  ],
  template: `
    <div class="staff-profile-container" *ngIf="staffMember$ | async as staff">
      <!-- Profile Header -->
      <mat-card class="profile-header">
        <mat-card-content>
          <div class="profile-header-content">
            <div class="avatar-section">
              <div class="avatar">
                <mat-icon *ngIf="!staff.avatar">account_circle</mat-icon>
                <img *ngIf="staff.avatar" [src]="staff.avatar" [alt]="staff.firstName + ' ' + staff.lastName">
              </div>
              <button mat-icon-button [matMenuTriggerFor]="avatarMenu" *ngIf="canEdit">
                <mat-icon>edit</mat-icon>
              </button>
            </div>
            
            <div class="profile-info">
              <h1>{{ staff.firstName }} {{ staff.lastName }}</h1>
              <h2>{{ staff.position }}</h2>
              <p class="department">{{ staff.department }}</p>
              
              <div class="status-chips">
                <mat-chip-set>
                  <mat-chip [class]="'status-' + staff.status">{{ staff.status | titlecase }}</mat-chip>
                  <mat-chip>{{ staff.employmentType | titlecase }}</mat-chip>
                  <mat-chip>{{ staff.accessLevel | titlecase }}</mat-chip>
                </mat-chip-set>
              </div>
              
              <div class="contact-info">
                <div class="contact-item">
                  <mat-icon>email</mat-icon>
                  <span>{{ staff.email }}</span>
                </div>
                <div class="contact-item">
                  <mat-icon>phone</mat-icon>
                  <span>{{ staff.phone }}</span>
                </div>
                <div class="contact-item">
                  <mat-icon>work</mat-icon>
                  <span>Employee ID: {{ staff.employeeId }}</span>
                </div>
              </div>
            </div>
            
            <div class="profile-actions" *ngIf="canEdit">
              <button mat-raised-button color="primary" (click)="editProfile()">
                <mat-icon>edit</mat-icon>
                Edit Profile
              </button>
              <button mat-button [matMenuTriggerFor]="actionsMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Profile Tabs -->
      <mat-tab-group class="profile-tabs" animationDuration="300ms">
        <!-- Overview Tab -->
        <mat-tab label="Overview">
          <div class="tab-content">
            <div class="overview-grid">
              <!-- Quick Stats -->
              <mat-card class="stats-card">
                <mat-card-header>
                  <mat-card-title>Quick Stats</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="stats-grid">
                    <div class="stat-item">
                      <span class="stat-value">{{ (activeGoals$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Active Goals</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ (pendingTasks$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Pending Tasks</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ (upcomingShifts$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Upcoming Shifts</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ (pendingTimeOff$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Pending Time Off</span>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>

              <!-- Recent Activity -->
              <mat-card class="activity-card">
                <mat-card-header>
                  <mat-card-title>Recent Activity</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <mat-list>
                    <mat-list-item *ngFor="let activity of recentActivity">
                      <mat-icon matListItemIcon>{{ getActivityIcon(activity.type) }}</mat-icon>
                      <div matListItemTitle>{{ activity.description }}</div>
                      <div matListItemLine>{{ activity.timestamp | date:'short' }}</div>
                    </mat-list-item>
                  </mat-list>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Goals Tab -->
        <mat-tab label="Goals" [badge]="(activeGoals$ | async)?.length">
          <div class="tab-content">
            <div class="section-header">
              <h3>Goals & Objectives</h3>
              <button mat-raised-button color="primary" *ngIf="canEdit" (click)="createGoal()">
                <mat-icon>add</mat-icon>
                Add Goal
              </button>
            </div>
            
            <div class="goals-grid">
              <mat-card *ngFor="let goal of goals$ | async" class="goal-card">
                <mat-card-header>
                  <mat-card-title>{{ goal.title }}</mat-card-title>
                  <mat-card-subtitle>{{ goal.category | titlecase }} • {{ goal.type | titlecase }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ goal.description }}</p>
                  <div class="goal-progress">
                    <div class="progress-info">
                      <span>Progress: {{ goal.progress }}%</span>
                      <span class="due-date">Due: {{ goal.targetDate | date:'mediumDate' }}</span>
                    </div>
                    <mat-progress-bar mode="determinate" [value]="goal.progress"></mat-progress-bar>
                  </div>
                  <div class="goal-status">
                    <mat-chip [class]="'status-' + goal.status">{{ goal.status | titlecase }}</mat-chip>
                    <mat-chip [class]="'priority-' + goal.priority">{{ goal.priority | titlecase }}</mat-chip>
                  </div>
                </mat-card-content>
                <mat-card-actions *ngIf="canEdit">
                  <button mat-button (click)="editGoal(goal)">Edit</button>
                  <button mat-button (click)="viewGoalDetails(goal)">Details</button>
                </mat-card-actions>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Tasks Tab -->
        <mat-tab label="Tasks" [badge]="(pendingTasks$ | async)?.length">
          <div class="tab-content">
            <div class="section-header">
              <h3>Tasks & Assignments</h3>
              <button mat-raised-button color="primary" *ngIf="canEdit" (click)="createTask()">
                <mat-icon>add</mat-icon>
                Assign Task
              </button>
            </div>
            
            <div class="tasks-list">
              <mat-card *ngFor="let task of tasks$ | async" class="task-card">
                <mat-card-content>
                  <div class="task-header">
                    <h4>{{ task.title }}</h4>
                    <div class="task-meta">
                      <mat-chip [class]="'status-' + task.status">{{ task.status | titlecase }}</mat-chip>
                      <mat-chip [class]="'priority-' + task.priority">{{ task.priority | titlecase }}</mat-chip>
                    </div>
                  </div>
                  <p *ngIf="task.description">{{ task.description }}</p>
                  <div class="task-details">
                    <div class="task-info">
                      <mat-icon>schedule</mat-icon>
                      <span *ngIf="task.dueDate">Due: {{ task.dueDate | date:'mediumDate' }}</span>
                      <span *ngIf="!task.dueDate">No due date</span>
                    </div>
                    <div class="task-info" *ngIf="task.estimatedHours">
                      <mat-icon>timer</mat-icon>
                      <span>{{ task.estimatedHours }}h estimated</span>
                    </div>
                  </div>
                </mat-card-content>
                <mat-card-actions *ngIf="canEdit">
                  <button mat-button (click)="editTask(task)">Edit</button>
                  <button mat-button (click)="viewTaskDetails(task)">Details</button>
                  <button mat-button *ngIf="task.status !== 'completed'" (click)="markTaskComplete(task)">
                    Complete
                  </button>
                </mat-card-actions>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Schedule Tab -->
        <mat-tab label="Schedule">
          <div class="tab-content">
            <div class="section-header">
              <h3>Work Schedule</h3>
              <button mat-raised-button color="primary" *ngIf="canEdit" (click)="viewFullCalendar()">
                <mat-icon>calendar_view_month</mat-icon>
                Full Calendar
              </button>
            </div>
            
            <!-- Personal Calendar Component will go here -->
            <mat-card class="schedule-card">
              <mat-card-content>
                <p>Personal calendar integration coming soon...</p>
                <!-- This will be replaced with a mini FullCalendar component -->
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Time Management Tab -->
        <mat-tab label="Time Management">
          <div class="tab-content">
            <div class="time-management-grid">
              <!-- Time Off Requests -->
              <mat-card class="time-off-card">
                <mat-card-header>
                  <mat-card-title>Time Off Requests</mat-card-title>
                  <button mat-icon-button *ngIf="canEdit" (click)="requestTimeOff()">
                    <mat-icon>add</mat-icon>
                  </button>
                </mat-card-header>
                <mat-card-content>
                  <mat-list>
                    <mat-list-item *ngFor="let request of timeOffRequests$ | async">
                      <div matListItemTitle>{{ request.type | titlecase }}</div>
                      <div matListItemLine>
                        {{ request.startDate | date:'mediumDate' }} - {{ request.endDate | date:'mediumDate' }}
                      </div>
                      <mat-chip matListItemMeta [class]="'status-' + request.status">
                        {{ request.status | titlecase }}
                      </mat-chip>
                    </mat-list-item>
                  </mat-list>
                </mat-card-content>
              </mat-card>

              <!-- Recent Time Entries -->
              <mat-card class="time-entries-card">
                <mat-card-header>
                  <mat-card-title>Recent Time Entries</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <mat-list>
                    <mat-list-item *ngFor="let entry of recentTimeEntries$ | async">
                      <mat-icon matListItemIcon>{{ getTimeEntryIcon(entry.type) }}</mat-icon>
                      <div matListItemTitle>{{ entry.type | titlecase }}</div>
                      <div matListItemLine>{{ entry.timestamp | date:'medium' }}</div>
                    </mat-list-item>
                  </mat-list>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>

      <!-- Action Menus -->
      <mat-menu #avatarMenu="matMenu">
        <button mat-menu-item (click)="uploadAvatar()">
          <mat-icon>upload</mat-icon>
          Upload Photo
        </button>
        <button mat-menu-item (click)="removeAvatar()" *ngIf="staff.avatar">
          <mat-icon>delete</mat-icon>
          Remove Photo
        </button>
      </mat-menu>

      <mat-menu #actionsMenu="matMenu">
        <button mat-menu-item (click)="viewFullProfile()">
          <mat-icon>person</mat-icon>
          View Full Profile
        </button>
        <button mat-menu-item (click)="exportProfile()">
          <mat-icon>download</mat-icon>
          Export Profile
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="deactivateStaff()" *ngIf="staff.status === 'active'">
          <mat-icon>person_off</mat-icon>
          Deactivate
        </button>
      </mat-menu>
    </div>
  `,
  styleUrls: ['./staff-profile.component.scss']
})
export class StaffProfileComponent implements OnInit {
  @Input() staffId?: string;

  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private staffService = inject(StaffFirestoreService);
  private calendarService = inject(CalendarService);
  private authService = inject(AuthService);

  staffMember$!: Observable<StaffMember | null>;
  goals$!: Observable<StaffGoalExtended[]>;
  tasks$!: Observable<StaffTask[]>;
  timeOffRequests$!: Observable<TimeOffRequest[]>;
  recentTimeEntries$!: Observable<TimeEntry[]>;
  upcomingShifts$!: Observable<CalendarEvent[]>;

  // Computed observables
  activeGoals$!: Observable<StaffGoalExtended[]>;
  pendingTasks$!: Observable<StaffTask[]>;
  pendingTimeOff$!: Observable<TimeOffRequest[]>;

  canEdit = false;
  recentActivity: any[] = [];

  ngOnInit(): void {
    // Get staff ID from route or input
    const staffIdFromRoute = this.route.snapshot.paramMap.get('id');
    const currentStaffId = this.staffId || staffIdFromRoute;

    if (!currentStaffId) {
      this.router.navigate(['/staff']);
      return;
    }

    // Initialize observables
    this.initializeData(currentStaffId);
    
    // Check permissions
    this.checkEditPermissions(currentStaffId);
  }

  private initializeData(staffId: string): void {
    // Staff member data
    this.staffMember$ = this.staffService.subscribeToStaffMember(staffId).pipe(shareReplay(1));

    // Goals data
    this.goals$ = this.staffService.subscribeToStaffGoals(staffId).pipe(shareReplay(1));
    this.activeGoals$ = this.goals$.pipe(
      map(goals => goals.filter(g => g.status === 'in-progress' || g.status === 'not-started'))
    );

    // Tasks data
    this.tasks$ = this.staffService.subscribeToStaffTasks(staffId).pipe(shareReplay(1));
    this.pendingTasks$ = this.tasks$.pipe(
      map(tasks => tasks.filter(t => t.status === 'pending' || t.status === 'in-progress'))
    );

    // Time management data
    this.timeOffRequests$ = this.staffService.getTimeOffRequestsByStaff(staffId).pipe(shareReplay(1));
    this.pendingTimeOff$ = this.timeOffRequests$.pipe(
      map(requests => requests.filter(r => r.status === 'pending'))
    );

    // Recent time entries (last 10)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    this.recentTimeEntries$ = this.staffService.getTimeEntriesByStaff(staffId, thirtyDaysAgo).pipe(
      map(entries => entries.slice(0, 10)),
      shareReplay(1)
    );

    // Upcoming shifts (next 7 days)
    const today = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);
    this.upcomingShifts$ = this.calendarService.getEventsByStaff(staffId, today, nextWeek).pipe(
      map(events => events.filter(e => e.type === 'shift')),
      shareReplay(1)
    );
  }

  private checkEditPermissions(staffId: string): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile) {
        // Can edit if it's their own profile or if they're admin/manager
        this.canEdit = profile.staffId === staffId || 
                      profile.role === 'admin' || 
                      profile.role === 'manager';
      }
    });
  }

  // Action methods
  editProfile(): void {
    this.router.navigate(['/staff', this.staffId || this.route.snapshot.paramMap.get('id'), 'edit']);
  }

  createGoal(): void {
    // Navigate to goal creation
  }

  editGoal(goal: StaffGoalExtended): void {
    // Navigate to goal editing
  }

  viewGoalDetails(goal: StaffGoalExtended): void {
    // Open goal details dialog
  }

  createTask(): void {
    // Navigate to task creation
  }

  editTask(task: StaffTask): void {
    // Navigate to task editing
  }

  viewTaskDetails(task: StaffTask): void {
    // Open task details dialog
  }

  markTaskComplete(task: StaffTask): void {
    this.staffService.updateTask(task.id, { 
      status: 'completed',
      completedDate: new Date()
    }).subscribe();
  }

  viewFullCalendar(): void {
    this.router.navigate(['/calendar'], { 
      queryParams: { staffId: this.staffId || this.route.snapshot.paramMap.get('id') }
    });
  }

  requestTimeOff(): void {
    // Open time off request dialog
  }

  uploadAvatar(): void {
    // Implement avatar upload
  }

  removeAvatar(): void {
    // Remove avatar
  }

  viewFullProfile(): void {
    // Navigate to full profile view
  }

  exportProfile(): void {
    // Export profile data
  }

  deactivateStaff(): void {
    // Deactivate staff member
  }

  // Utility methods
  getActivityIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'login': 'login',
      'schedule-change': 'schedule',
      'task-completed': 'check_circle',
      'message-sent': 'message',
      'profile-updated': 'person'
    };
    return icons[type] || 'info';
  }

  getTimeEntryIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'clock-in': 'login',
      'clock-out': 'logout',
      'break-start': 'pause',
      'break-end': 'play_arrow',
      'lunch-start': 'restaurant',
      'lunch-end': 'restaurant'
    };
    return icons[type] || 'schedule';
  }
}
