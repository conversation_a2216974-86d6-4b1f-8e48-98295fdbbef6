import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Services
import { AuthService } from '../../../../core/auth/auth.service';
import { StaffFirestoreService } from '../../../staff/services/staff-firestore.service';

// Models
import { StaffMember } from '../../../staff/models/staff.model';

export interface StaffAvailability {
  staffId: string;
  staffName: string;
  position: string;
  weeklyAvailability: {
    [key: string]: {
      available: boolean;
      startTime?: string;
      endTime?: string;
      notes?: string;
    };
  };
  timeOffRequests: any[];
  totalHoursAvailable: number;
}

@Component({
  selector: 'app-staff-availability',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatChipsModule,
    MatTooltipModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="availability-container">
      <div class="availability-header">
        <h2>
          <mat-icon>people</mat-icon>
          Staff Availability Overview
        </h2>
        <div class="header-actions">
          <button mat-raised-button color="primary" (click)="refreshAvailability()">
            <mat-icon>refresh</mat-icon>
            Refresh
          </button>
          <button mat-raised-button color="accent" (click)="generateAIInsights()">
            <mat-icon>psychology</mat-icon>
            AI Insights
          </button>
        </div>
      </div>

      <div class="availability-filters">
        <mat-form-field appearance="outline">
          <mat-label>Filter by Position</mat-label>
          <mat-select [formControl]="positionFilter" multiple>
            <mat-option *ngFor="let position of availablePositions" [value]="position">
              {{ position }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>View Mode</mat-label>
          <mat-select [formControl]="viewModeFilter">
            <mat-option value="all">All Staff</mat-option>
            <mat-option value="available">Available Only</mat-option>
            <mat-option value="unavailable">Unavailable Only</mat-option>
            <mat-option value="limited">Limited Availability</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="availability-grid" *ngIf="filteredAvailability$ | async as availability; else loading">
        <mat-card *ngFor="let staff of availability" class="staff-availability-card">
          <mat-card-header>
            <div mat-card-avatar class="staff-avatar">
              <mat-icon>person</mat-icon>
            </div>
            <mat-card-title>{{ staff.staffName }}</mat-card-title>
            <mat-card-subtitle>
              {{ staff.position }} • {{ staff.totalHoursAvailable }}h/week available
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <div class="weekly-schedule">
              <div class="day-availability"
                   *ngFor="let day of daysOfWeek"
                   [class.available]="staff.weeklyAvailability[day]?.available"
                   [class.unavailable]="!staff.weeklyAvailability[day]?.available">
                <div class="day-name">{{ day.substring(0, 3) }}</div>
                <div class="day-hours" *ngIf="staff.weeklyAvailability[day]?.available">
                  {{ staff.weeklyAvailability[day]?.startTime }} -
                  {{ staff.weeklyAvailability[day]?.endTime }}
                </div>
                <div class="day-unavailable" *ngIf="!staff.weeklyAvailability[day]?.available">
                  Unavailable
                </div>
              </div>
            </div>

            <div class="time-off-summary" *ngIf="staff.timeOffRequests.length > 0">
              <h4>Upcoming Time Off:</h4>
              <mat-chip-set>
                <mat-chip *ngFor="let request of staff.timeOffRequests.slice(0, 3)">
                  {{ request.startDate | date:'MMM d' }} - {{ request.endDate | date:'MMM d' }}
                </mat-chip>
              </mat-chip-set>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-button color="primary" (click)="editAvailability(staff.staffId)">
              <mat-icon>edit</mat-icon>
              Edit
            </button>
            <button mat-button (click)="viewDetails(staff.staffId)">
              <mat-icon>visibility</mat-icon>
              Details
            </button>
            <button mat-button color="accent" (click)="getAIRecommendations(staff.staffId)">
              <mat-icon>psychology</mat-icon>
              AI Analysis
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <ng-template #loading>
        <div class="loading-container">
          <mat-spinner></mat-spinner>
          <p>Loading staff availability...</p>
        </div>
      </ng-template>

      <!-- AI Insights Panel -->
      <mat-card class="ai-insights-panel" *ngIf="aiInsights">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>psychology</mat-icon>
            AI Availability Insights
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div [innerHTML]="aiInsights"></div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button (click)="aiInsights = null">Close</button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styleUrls: ['./staff-availability.component.scss']
})
export class StaffAvailabilityComponent implements OnInit {
  private authService = inject(AuthService);
  private staffService = inject(StaffFirestoreService);
  private fb = inject(FormBuilder);

  // Form controls
  positionFilter = this.fb.control([]);
  viewModeFilter = this.fb.control('all');

  // Data
  staffMembers$!: Observable<StaffMember[]>;
  filteredAvailability$!: Observable<StaffAvailability[]>;
  availablePositions: string[] = [];
  daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // AI Insights
  aiInsights: string | null = null;

  ngOnInit(): void {
    this.loadStaffAvailability();
  }

  private loadStaffAvailability(): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        this.staffMembers$ = this.staffService.getActiveStaff(profile.primaryBusinessId);

        this.filteredAvailability$ = combineLatest([
          this.staffMembers$,
          this.positionFilter.valueChanges,
          this.viewModeFilter.valueChanges
        ]).pipe(
          map(([staff, positionFilters, viewMode]) =>
            this.transformToAvailabilityData(staff, positionFilters, viewMode)
          )
        );

        // Extract available positions
        this.staffMembers$.subscribe(staff => {
          this.availablePositions = [...new Set(staff.map(s => s.position))];
        });
      }
    });
  }

  private transformToAvailabilityData(
    staff: StaffMember[],
    positionFilters: string[],
    viewMode: string
  ): StaffAvailability[] {
    let filteredStaff = staff;

    // Apply position filter
    if (positionFilters && positionFilters.length > 0) {
      filteredStaff = staff.filter(s => positionFilters.includes(s.position));
    }

    // Transform to availability data
    const availabilityData = filteredStaff.map(member => ({
      staffId: member.id || '',
      staffName: `${member.firstName} ${member.lastName}`,
      position: member.position,
      weeklyAvailability: member.availability || this.getDefaultAvailability(),
      timeOffRequests: [], // TODO: Load from time off service
      totalHoursAvailable: this.calculateTotalHours(member.availability || {})
    }));

    // Apply view mode filter
    switch (viewMode) {
      case 'available':
        return availabilityData.filter(a => a.totalHoursAvailable > 0);
      case 'unavailable':
        return availabilityData.filter(a => a.totalHoursAvailable === 0);
      case 'limited':
        return availabilityData.filter(a => a.totalHoursAvailable > 0 && a.totalHoursAvailable < 40);
      default:
        return availabilityData;
    }
  }

  private getDefaultAvailability(): any {
    const defaultAvailability: any = {};
    this.daysOfWeek.forEach(day => {
      defaultAvailability[day] = {
        available: false,
        startTime: '09:00',
        endTime: '17:00',
        notes: ''
      };
    });
    return defaultAvailability;
  }

  private calculateTotalHours(availability: any): number {
    let totalHours = 0;
    this.daysOfWeek.forEach(day => {
      if (availability[day]?.available) {
        const start = new Date(`2000-01-01 ${availability[day].startTime}`);
        const end = new Date(`2000-01-01 ${availability[day].endTime}`);
        totalHours += (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      }
    });
    return totalHours;
  }

  refreshAvailability(): void {
    this.loadStaffAvailability();
  }

  async generateAIInsights(): Promise<void> {
    // TODO: Implement AI insights using Gemini 2.5 Flash
    this.aiInsights = `
      <h3>Availability Analysis</h3>
      <p><strong>Coverage Gaps:</strong> Low coverage on weekends and Monday mornings.</p>
      <p><strong>Recommendations:</strong></p>
      <ul>
        <li>Consider incentives for weekend shifts</li>
        <li>Cross-train staff for better flexibility</li>
        <li>Review Monday morning staffing needs</li>
      </ul>
    `;
  }

  editAvailability(staffId: string): void {
    // TODO: Open availability edit dialog
    console.log('Edit availability for:', staffId);
  }

  viewDetails(staffId: string): void {
    // TODO: Navigate to detailed availability view
    console.log('View details for:', staffId);
  }

  async getAIRecommendations(staffId: string): Promise<void> {
    // TODO: Get AI recommendations for specific staff member
    console.log('Get AI recommendations for:', staffId);
  }
}
