.time-off-container {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;

  .time-off-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 4px;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: #333;

      mat-icon {
        color: #1976d2;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }

  .time-off-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;

    ::ng-deep .mat-mdc-tab-group {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    ::ng-deep .mat-mdc-tab-body-wrapper {
      flex: 1;
      overflow: hidden;
    }

    ::ng-deep .mat-mdc-tab-body {
      height: 100%;
      overflow: auto;
    }

    .tab-content {
      padding: 24px 0;
      height: 100%;
      overflow: auto;
    }

    // Pending Requests Tab
    .requests-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 20px;

      .request-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        &.pending {
          border-left: 4px solid #ff9800;
        }

        .request-avatar {
          background: #1976d2;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .request-details {
          display: flex;
          flex-direction: column;
          gap: 12px;
          margin: 16px 0;

          > div {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;

            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
              color: #666;
            }
          }

          .reason {
            color: #555;
          }

          .requested-date {
            color: #888;
            font-size: 0.8rem;
          }
        }

        mat-card-actions {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          button {
            flex: 1;
            min-width: 80px;

            mat-icon {
              margin-right: 4px;
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      text-align: center;
      color: #666;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #4caf50;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        font-size: 1.25rem;
      }

      p {
        margin: 0;
        font-size: 0.875rem;
      }
    }

    // All Requests Table
    .requests-table {
      width: 100%;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      .type-vacation {
        background: #e3f2fd;
        color: #1976d2;
      }

      .type-sick {
        background: #ffebee;
        color: #d32f2f;
      }

      .type-personal {
        background: #f3e5f5;
        color: #7b1fa2;
      }

      .type-other {
        background: #e8f5e8;
        color: #388e3c;
      }

      .status-pending {
        background: #fff3e0;
        color: #f57c00;
      }

      .status-approved {
        background: #e8f5e8;
        color: #388e3c;
      }

      .status-denied {
        background: #ffebee;
        color: #d32f2f;
      }

      mat-chip {
        font-size: 0.75rem;
        height: 24px;
        border-radius: 12px;
      }
    }

    // Balances Tab
    .balances-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;

      .balance-card {
        .balance-avatar {
          background: #1976d2;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .balance-summary {
          display: flex;
          flex-direction: column;
          gap: 16px;
          margin: 16px 0;

          .balance-item {
            padding: 12px;
            background: #f5f5f5;
            border-radius: 6px;

            h4 {
              margin: 0 0 8px 0;
              font-size: 0.875rem;
              font-weight: 600;
              color: #333;
            }

            .balance-details {
              .remaining {
                font-size: 1rem;
                font-weight: 500;
                color: #1976d2;
                display: block;
                margin-bottom: 4px;
              }

              .balance-breakdown {
                font-size: 0.75rem;
                color: #666;
              }
            }
          }
        }

        mat-card-actions {
          display: flex;
          gap: 8px;

          button {
            flex: 1;

            mat-icon {
              margin-right: 4px;
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .time-off-container {
    .time-off-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .time-off-tabs {
      .requests-grid {
        grid-template-columns: 1fr;
      }

      .balances-grid {
        grid-template-columns: 1fr;
      }

      .requests-table {
        font-size: 0.875rem;

        mat-chip {
          font-size: 0.7rem;
          height: 20px;
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .time-off-container {
    .time-off-header h2 {
      color: #e0e0e0;
    }

    .time-off-tabs {
      .requests-table {
        background: #424242;
      }

      .balance-summary .balance-item {
        background: #424242;

        h4 {
          color: #e0e0e0;
        }
      }
    }
  }
}
